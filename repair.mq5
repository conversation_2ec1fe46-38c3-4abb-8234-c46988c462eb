//@version=6
strategy("Gold Pro Strategy", overlay=true, initial_capital=10000, commission_type=strategy.commission.cash_per_order, commission_value=0.0001, default_qty_type=strategy.percent_of_equity, default_qty_value=100)

// 1. Input Parameters
emaFastLen = input.int(50, "Fast EMA Length")
emaSlowLen = input.int(200, "Slow EMA Length")
rsiLength = input.int(14, "RSI Length")
atrLength = input.int(14, "ATR Length")
trailATRMult = input.float(3.0, "ATR Trail Multiplier")

// 2. Calculate Indicators
emaFast = ta.ema(close, emaFastLen)
emaSlow = ta.ema(close, emaSlowLen)
[macdLine, signalLine, _] = ta.macd(close, 12, 26, 9)
rsi = ta.rsi(close, rsiLength)
atr = ta.atr(atrLength)

// 3. Trend Conditions
bullishTrend = emaFast > emaSlow and close > emaSlow
bearishTrend = emaFast < emaSlow and close < emaSlow

// 4. Momentum Signals
macdBullish = ta.crossover(macdLine, signalLine)
macdBearish = ta.crossunder(macdLine, signalLine)
rsiBullish = rsi > 50 and rsi < 70
rsiBearish = rsi < 50 and rsi > 30

// 5. Entry/Exit Logic
longCondition = bullishTrend and macdBullish and rsiBullish
shortCondition = bearishTrend and macdBearish and rsiBearish

// 6. Risk Management
trailOffset = atr * trailATRMult
longStopPrice = close - trailOffset
shortStopPrice = close + trailOffset

// 7. Strategy Execution
if (longCondition)
    strategy.entry("Long", strategy.long)
    strategy.exit("Long Exit", "Long", stop=longStopPrice, trail_points=close*0.01, trail_offset=trailOffset)

if (shortCondition)
    strategy.entry("Short", strategy.short)
    strategy.exit("Short Exit", "Short", stop=shortStopPrice, trail_points=close*0.01, trail_offset=trailOffset)

// 8. Visualizations
plot(emaFast, color=color.blue, linewidth=2)
plot(emaSlow, color=color.red, linewidth=2)
plotshape(longCondition, style=shape.triangleup, location=location.belowbar, color=color.green, size=size.small)
plotshape(shortCondition, style=shape.triangledown, location=location.abovebar, color=color.red, size=size.small)